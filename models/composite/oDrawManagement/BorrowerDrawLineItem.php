<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\composite\oDrawManagement\traits\PropertiesMapper;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\tblDrawRequestLineItemDocs;
use models\Database2;

class BorrowerDrawLineItem extends strongType
{
    use PropertiesMapper;

    public ?int $id = null;
    public ?int $drawId = null;
    public ?int $categoryId = null;
    public ?string $name = null;
    public ?string $description = null;
    public ?int $order = 1;
    public ?float $cost = 0.00;
    public ?float $completedAmount = 0.00;
    public ?float $completedPercent = 0.00;
    public ?float $requestedAmount = 0.00;
    public ?float $disbursedAmount = 0.00;
    public ?string $notes = null;
    public ?string $lenderNotes = null;
    public ?string $rejectReason = null;
    public ?string $createdAt = null;
    public ?string $updatedAt = null;
    public ?tblDrawRequestLineItems $lineItem = null;

    /**
     * BorrowerDrawLineItem constructor.
     * @param tblDrawRequestLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems $lineItem = null) {
        if ($lineItem == null) $lineItem = new tblDrawRequestLineItems();
        $this->setProperties($lineItem);
    }

    /**
     * Saves the current line item object to the database.
     * @return array The result of the save operation.
     */
    public function save(array $lineItemData): array {
        $this->setFromArray($lineItemData);
        $saved = $this->lineItem->save();
        $this->id = $this->lineItem->id;
        return $saved;
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->lineItem->id = $lineItemData['id'] ?? null;
        $this->lineItem->drawId = $lineItemData['drawId'];
        $this->lineItem->categoryId = $lineItemData['categoryId'];
        $this->lineItem->name = $lineItemData['name'];
        $this->lineItem->description = $lineItemData['description'] ?? $this->lineItem->description;
        $this->lineItem->order = $lineItemData['order'] ?? $this->lineItem->order;
        $this->lineItem->cost = $lineItemData['cost'] ?? $this->lineItem->cost;
        $this->lineItem->completedAmount = $lineItemData['completedAmount'] ?? $this->lineItem->completedAmount;
        $this->lineItem->completedPercent = $lineItemData['completedPercent'] ?? $this->lineItem->completedPercent;
        $this->lineItem->requestedAmount = $lineItemData['requestedAmount'] ?? $this->lineItem->requestedAmount;
        $this->lineItem->disbursedAmount = $lineItemData['disbursedAmount'] ?? $this->lineItem->disbursedAmount;
        $this->lineItem->notes = $lineItemData['notes'] ?? $this->lineItem->notes;
        $this->lineItem->lenderNotes = $lineItemData['lenderNotes'] ?? $this->lineItem->lenderNotes;
        $this->lineItem->rejectReason = $lineItemData['rejectReason'] ?? $this->lineItem->rejectReason;
        $this->setProperties($this->lineItem);
    }

    public function getDbObject(): tblDrawRequestLineItems {
        return $this->lineItem;
    }

    /**
     * Delete the lineitem from DB
     *
     * @return void
     */
    public function delete(): void {
        if ($this->lineItem instanceof tblDrawRequestLineItems) {
            $this->lineItem->delete();
        }
    }

    /**
     * Converts the line item object to an associative array.
     * @return array An associative array representation of the line item.
     */
    public function toArray(): array {
        $reflector = new \ReflectionClass($this->lineItem);
        $publicProps = $reflector->getProperties(\ReflectionProperty::IS_PUBLIC);
        array_map(function ($prop) use (&$data) {
            $val = $prop->getValue($this->lineItem);
            if (is_null($val) && $prop->gettype()->getName() === 'string') {
                $val = '';
            };
            $data[$prop->getName()] = $val;

        }, $publicProps);

        return $data;
    }

    /**
     * Get documents associated with this line item
     *
     * @return array Array of document data
     */
    public function getDocuments(): array {
        if (!$this->id) {
            return [];
        }

        $db = Database2::getInstance();
        $qry = 'SELECT
                    ld.id as lineItemDocId,
                    ld.uploadedBy,
                    ld.uploadedDate,
                    ld.uploaderType,
                    d.docID,
                    d.docName,
                    d.displayDocName,
                    d.docCategory,
                    d.uploadedDate as docUploadedDate,
                    d.LMRId,
                    f.recordDate,
                    f.oldFPCID
                FROM tblDrawRequestLineItemDocs ld
                JOIN tblLMRFileDocs d ON d.docID = ld.docId
                JOIN tblFile f ON f.LMRId = d.LMRId
                WHERE ld.lineItemId = :lineItemId
                AND ld.activeStatus = 1
                AND d.activeStatus = 1
                ORDER BY ld.uploadedDate DESC';

        return $db->queryData($qry, ['lineItemId' => $this->id]);
    }

    /**
     * Add a document to this line item
     *
     * @param int $docId Document ID from tblLMRFileDocs
     * @param int $uploadedBy User ID who uploaded
     * @param string $uploaderType Type of uploader (borrower/lender)
     * @return bool Success status
     */
    public function addDocument(int $docId, int $uploadedBy, string $uploaderType = 'borrower'): bool {
        if (!$this->id) {
            return false;
        }

        $lineItemDoc = new tblDrawRequestLineItemDocs();
        $lineItemDoc->lineItemId = $this->id;
        $lineItemDoc->docId = $docId;
        $lineItemDoc->uploadedBy = $uploadedBy;
        $lineItemDoc->uploadedDate = date('Y-m-d H:i:s');
        $lineItemDoc->uploaderType = $uploaderType;
        $lineItemDoc->activeStatus = 1;

        $result = $lineItemDoc->Save();
        return !empty($result);
    }

    /**
     * Remove a document from this line item (soft delete)
     *
     * @param int $docId Document ID to remove
     * @return bool Success status
     */
    public function removeDocument(int $docId): bool {
        if (!$this->id) {
            return false;
        }

        $db = Database2::getInstance();
        $qry = 'UPDATE tblDrawRequestLineItemDocs
                SET activeStatus = 0
                WHERE lineItemId = :lineItemId AND docId = :docId';

        $result = $db->queryData($qry, [
            'lineItemId' => $this->id,
            'docId' => $docId
        ]);

        return $result !== false;
    }

    /**
     * Check if a document is already associated with this line item
     *
     * @param int $docId Document ID to check
     * @return bool True if document is associated
     */
    public function hasDocument(int $docId): bool {
        if (!$this->id) {
            return false;
        }

        $db = Database2::getInstance();
        $qry = 'SELECT COUNT(*) as count
                FROM tblDrawRequestLineItemDocs
                WHERE lineItemId = :lineItemId
                AND docId = :docId
                AND activeStatus = 1';

        $result = $db->queryData($qry, [
            'lineItemId' => $this->id,
            'docId' => $docId
        ]);

        return ($result[0]['count'] ?? 0) > 0;
    }

    /**
     * Generate document view URL for a document
     *
     * @param array $document Document data from getDocuments()
     * @return string Document view URL
     */
    public static function generateDocumentViewUrl(array $document): string
    {
        $docName = $document['docName'];
        $displayDocName = $document['displayDocName'] ?: $docName;
        $recordDate = $document['recordDate'];
        $oldFPCID = $document['oldFPCID'];
        $LMRId = $document['LMRId'];

        // Construct folder path following the same pattern as other document views
        $tempRecDate = str_replace('-', '', $recordDate);
        $folderName = $oldFPCID . '/' . date('Y', strtotime($tempRecDate)) . '/' . date('m', strtotime($tempRecDate)) . '/' . date('d', strtotime($tempRecDate));
        $fP = $folderName . '/' . $LMRId . '/upload';

        // Generate the view URL using the same pattern as other document views
        $uploadDocUrl = CONST_URL_BOSSL . 'viewDocuments.php?fn=' . \models\cypher::myEncryption($docName) .
                       '&fd=' . \models\cypher::myEncryption(CONST_PATH_LMR_FILE_DOCS . $fP) .
                       '&opt=enc&dn=' . \models\cypher::myEncryption(str_replace(' ', '_', $displayDocName));

        return $uploadDocUrl;
    }
}
